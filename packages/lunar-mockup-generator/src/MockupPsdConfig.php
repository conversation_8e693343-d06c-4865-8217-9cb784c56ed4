<?php

namespace Swebvn\LunarMockupGenerator;

use App\Models\CollectionTypeCode as TypeCode;

class MockupPsdConfig
{
    public static function getCollectionsConfig(): array
    {
        return [
            [
                'type_code' => TypeCode::Bags->value,
                'templates' => [
                    [
                        'name'     => 'bag_02',
                        'primary' => false,
                        'bg_image' => ''
                    ],
                    [
                        'name'     => 'bag_01',
                        'primary' => true,
                        'bg_image' => ''
                    ],
                ]
            ],
            [
                'type_code' => TypeCode::HatsAndCaps->value,
                'templates' => [
                    [
                        'name'     => 'hat_01',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                ]
            ],
            [
                'type_code' => TypeCode::MousePads->value,
                'templates' => [
                    [
                        'name'     => 'mousepad_01',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                ]
            ],
            [
                'type_code' => TypeCode::Mugs->value,
                'templates' => [
                    [
                        'name'     => 'mug_01',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                ]
            ],
            [
                'type_code' => TypeCode::IPhoneCases->value,
                'templates' => [
                    [
                        'name'     => 'phonecase_01',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                    [
                        'name'     => 'phonecase_02',
                        'bg_image' => '',
                        'primary' => false,
                    ],
                ]
            ],
            [
                'type_code' => TypeCode::PillowsCover->value,
                'templates' => [
                    [
                        'name'     => 'pillowwhite_01',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                    [
                        'name'     => 'pillowwhite_02',
                        'bg_image' => '',
                        'primary' => false,
                    ],
                ]
            ],
            [
                'type_code' => TypeCode::Pins->value,
                'templates' => [
                    [
                        'name'     => 'pin_01',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                ]
            ],
            [
                'type_code' => TypeCode::Posters->value,
                'templates' => [
                    [
                        'name'     => 'poster_01',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                ]
            ],
            [
                'type_code' => TypeCode::Sweatshirts->value,
                'templates' => [
                    [
                        'name'     => 'sweatshirt_02',
                        'bg_image' => '',
                        'primary' => false,
                    ],
                    [
                        'name'     => 'sweatshirt_01',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                ]
            ],
            [
                'type_code' => TypeCode::TankTops->value,
                'templates' => [
                    [
                        'name'     => 'tanktopmale_02',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                ]
            ],
            [
                'type_code' => TypeCode::Tapestries->value,
                'templates' => [
                    [
                        'name'     => 'tapestries_01',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                ]
            ],
            [
                'type_code' => TypeCode::Backpacks->value,
                'templates' => [
                    [
                        'name'     => 'backpack_01',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                ]
            ],
/*             [
                'type_code' => TypeCode::FleeceBlanket->value,
                'templates' => [
                    [
                        'name'     => 'blanket_01',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                    [
                        'name'     => 'blanket_02',
                        'bg_image' => '',
                        'primary' => false,
                    ],
                ]
            ], */
            [
                'type_code' => TypeCode::BathMats->value,
                'templates' => [
                    [
                        'name'     => 'bathmath_01',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                ]
            ],
            [
                'type_code' => TypeCode::Hoodies->value,
                'templates' => [
                    [
                        'name'     => 'hoodie_05',
                        'bg_image' => '',
                        'primary' => false,
                    ],
                    [
                        'name'     => 'hoodie_04',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                ]
            ],
            [
                'type_code' => TypeCode::TShirts->value,
                'templates' => [
                    [
                        'name'     => 'tshirt_06',
                        'bg_image' => '',
                        'primary' => false,
                    ],
                    [
                        'name'     => 'tshirt_16',
                        'bg_image' => '',
                        'primary' => false,
                    ],
                    [
                        'name'     => 'tshirt_15',
                        'bg_image' => '',
                        'primary' => true,
                    ],
                ]
            ],

        ];
    }
}
