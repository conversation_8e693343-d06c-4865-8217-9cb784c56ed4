<?php

namespace Swebvn\LunarMockupGenerator\Actions;

use App\Actions\CheckImageUrl;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Lunar\Models\Collection;
use Swebvn\LunarMockupGenerator\Actions\ConvertWebpToJpegAction;
use Swebvn\LunarMockupGenerator\Mockup;
use Swebvn\LunarMockupGenerator\MockupPsdConfig;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GenerateProductsForMockupV2
{
    use AsAction;

    public $jobQueue = 'fast';

    public function handle(Mockup $mockup, string $designCode): void
    {
        $config = MockupPsdConfig::getCollectionsConfig();

        $designImageUrl = $mockup->imageUrl;

        if (CheckImageUrl::make()->handle($designImageUrl) === false) {
            return;
        }

        $payload = [
            'mockup_name' => $mockup->name,
            'collections' => []
        ];

        foreach ($config as $cc) {
            $collection = Collection::where('type_code', $cc['type_code'])->first();

            if (!$collection) {
                continue;
            }
            
            $templates = [];
            foreach ($cc['templates'] as $template) {
               $templates[] = $template['name'];
            }

            $payload['collections'][] = [
                'type_code' => $cc['type_code'],
                'design_code' => $designCode,
                'templates' => $templates
            ];
        }

        // Call bulk API
        $this->callBulkApi($designImageUrl, $payload);
    }

    private function callBulkApi(string $imageUrl, array $payload): void
    {
        try {

            $imageContent = file_get_contents($imageUrl);
            if ($imageContent === false) {
                Log::error('Failed to download image from: ' . $imageUrl);
                return;
            }

            $extension = pathinfo(parse_url($imageUrl, PHP_URL_PATH), PATHINFO_EXTENSION) ?: 'jpg';

            $response = Http::attach('design_image', $imageContent, 'design.' . $extension)
                        ->post('https://lunarmockup.tdagroup.online/api/lunar/jobs/bulk', [
                            'domain' => tenant()->getTenantKey(),
                            'callback_url' => tenant()->getTenantKey() . '/api/mockup/v2',
                            'payload' => json_encode($payload)
                        ]);

            if ($response->successful()) {
                $responseData = $response->json();
                Log::info('Bulk jobs created successfully', [
                    'total_jobs' => $responseData['data']['total_created'] ?? 0,
                    'image_url' => $imageUrl
                ]);
            } else {
                Log::error('Failed to create bulk jobs', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception when calling bulk API', [
                'error' => $e->getMessage(),
                'image_url' => $imageUrl,
            ]);
        }
    }
}
