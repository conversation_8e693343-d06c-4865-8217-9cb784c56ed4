<?php

namespace Swebvn\LunarMockupGenerator\Actions;

use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use Lunar\FieldTypes\TranslatedText;
use Lunar\Models\Channel;
use Lunar\Models\Collection;
use Lunar\Models\Currency;
use Lunar\Models\CustomerGroup;
use Lunar\Models\Language;
use Lunar\Models\Product;
use Lunar\Models\ProductType;
use Lunar\Models\ProductVariant;
use Lunar\Models\TaxClass;
use Swebvn\LunarMockupGenerator\Mockup;

class GenerateMockupForCollectionV2
{
    use AsAction;

    public $jobQueue = 'fast';

    public function handle(string $typeCode, $mockupName, $productImage, $designImage, $designCode): void
    {
        $existingProduct = $this->checkExistingProduct($typeCode, $designCode);
        if ($existingProduct) {
            dispatch(fn() => $this->attachMedia($existingProduct, $productImage, ''))->onQueue('fast');
            return;
        }

        $collection = Collection::where('type_code', $typeCode)->first();

        $title = $mockupName . ' ' . $collection->attr('name');
        $title = trim($title);
        $description = $collection->defaultDescription();

        $attributeData = [
            'name'              => new TranslatedText(['en' => $title]),
            'description'       => new TranslatedText(['en' => $description]),
            'short_description' => new TranslatedText(['en' => ''])
        ];

        $product = Product::forceCreateQuietly([
            'attribute_data'  => $attributeData,
            'status'          => 'draft',
            'meta'            => [
                'combination_id' => $collection->meta['combination_id'] ?? null,
                'design_code' => $designCode ?? null,
            ],
            'product_type_id' => ProductType::whereName('Default')->first()?->id,
        ]);

        $product->defaultUrl()->create([
            'language_id' => Language::getDefault()->id,
            'slug'        => Str::slug($title) . '-' . strtolower(Str::random(3)),
            'default'     => true,
        ]);
        $product->collections()->attach($collection->id);

        $product->channels()->attach(Channel::getDefault(), ['enabled' => true]);
        $product->customerGroups()->attach(CustomerGroup::getDefault(), ['enabled' => true]);

        $sku = 'MOCK-' . $collection->type_code->value . '-' . time();
        /** @var ProductVariant $variant */
        $variant = $product->variants()->create([
            'sku'          => $sku,
            'tax_class_id' => TaxClass::getDefault()->id,
        ]);

        $variant->basePrices()->create([
            'currency_id'   => Currency::getDefault()->id,
            'price'         => ($collection->meta['base_price'] ?? 322) * 100,
            'compare_price' => null,
        ]);

        dispatch(fn() => $this->attachMedia($product, $productImage, $designImage))->onQueue('fast');

        $product->update(['status' => 'published']);
    }

    private function checkExistingProduct(string $typeCode, string $designCode): ?Product
    {
        return Product::whereHas('collections', fn($q) => 
                    $q->where('type_code', $typeCode)
                )
                ->where('meta->design_code', $designCode)
                ->first();
    }
    
    public function attachMedia(Product $product, ?string $productImage, ?string $designImage): void
    {
        $existingMedia = $product->getMedia('images');
    
        $existingHashes = $existingMedia
            ->mapWithKeys(fn($media) => [$media->getCustomProperty('source_hash') => true])
            ->toArray();
    
        $this->addImageIfNotExists($product, $productImage, $existingHashes, true);
    
        $this->addImageIfNotExists($product, $designImage, $existingHashes, false);
    }
    
    private function addImageIfNotExists(Product $product, ?string $imageUrl, array $existingHashes, bool $isPrimary = false): void
    {
        if (!$imageUrl || !filter_var($imageUrl, FILTER_VALIDATE_URL)) {
            return;
        }
    
        $hash = md5($imageUrl);
    
        if (isset($existingHashes[$hash])) {
            return;
        }
    
        $product
            ->addMediaFromUrl($imageUrl)
            ->withCustomProperties([
                'primary' => $isPrimary,
                'source_hash' => $hash,
            ])
            ->toMediaCollection('images');
    }
    
    
}
