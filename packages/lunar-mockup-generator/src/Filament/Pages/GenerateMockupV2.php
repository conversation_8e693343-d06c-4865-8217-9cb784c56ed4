<?php

namespace Swebvn\LunarMockupGenerator\Filament\Pages;

use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Section;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Actions\Action;
use Filament\Forms\Components\Actions;
use Filament\Pages\Page;
use Filament\Forms\Components\Actions\Action as FormAction;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use Swebvn\LunarMockupGenerator\Actions\GenerateProductsForMockup;
use Swebvn\LunarMockupGenerator\Actions\GenerateProductsForMockupV2;
use Swebvn\LunarMockupGenerator\Mockup;
use Illuminate\Support\Str;

class GenerateMockupV2 extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-puzzle-piece';

    protected static ?string $navigationGroup = 'Plugins';

    protected static ?string $navigationLabel = 'Auto Mockup';

    protected static ?string $title = 'Auto Mockup';

    protected static string $view = 'lunar-mockup-generator::filament.pages.generate-mockup';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Auto Mockup Generation')
                    ->schema([
                        Repeater::make('mockupsV2')
                            ->hiddenLabel()
                            ->schema([
                                TextInput::make('name')
                                    ->maxLength(255)
                                    ->helperText('This name will be used as a part of the product title'),
                                FileUpload::make('image')
                                    ->image()
                                    ->required()
                                    ->disk('public')
                                    ->directory('mockups-v2'),
                            ])
                            ->reorderable(false)
                            ->columns(2)
                            ->defaultItems(1)
                            ->addActionLabel('Add Mockup'),

                        Actions::make([
                            Actions\Action::make('save')
                                ->label('Generate Mockups')
                                ->action('submit')
                                ->requiresConfirmation(false)
                        ])
                    ])
                    ->collapsible(),
            ])
            ->statePath('data');
    }

    public function submit(): void
    {
        $data = $this->form->getState();
        
        foreach ($data['mockupsV2'] as $item) {
            $designCode = 'DESIGN-' . strtoupper(Str::random(8));
            GenerateProductsForMockupV2::dispatch(new Mockup(
                $item['name'],
                Storage::disk('public')->url($item['image']),
            ), $designCode);
        }

        Notification::make()
            ->title('Products are being generated')
            ->info()
            ->send();
    }
}
