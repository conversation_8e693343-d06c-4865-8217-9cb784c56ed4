<?php

namespace Swebvn\LunarImportHub\Filament\Pages;

use App\Services\CustomEdge;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Cache;
use Swebvn\LunarImportHub\Actions\AutoRedbubble\CrawlForCollection;
use Swebvn\LunarImportHub\Actions\AutoRedbubble\InitCollectionConfig;
use Swebvn\LunarImportHub\CrawlCollectionConfig;
use Swebvn\LunarImportHub\Filament\ImportHubCluster;

/**
 * @property-read  Form form
 */
class AutoRedbubble extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $cluster = ImportHubCluster::class;

    protected static ?string $navigationIcon = 'heroicon-o-bug-ant';

    protected static string $view = 'lunar-woocommerce-importer::pages.auto-redbubble';

    public ?array $data = [
        'search_keyword'     => '',
        'compare_keywords'   => '',
        'suffix'             => '',
        'collections_config' => [],
    ];

    public function mount(): void
    {
        $this->data['collections_config'] = InitCollectionConfig::make();

        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->statePath('data')
            ->schema([
                Section::make()
                    ->columns(2)
                    ->schema([
                        TextInput::make('search_keyword')->required()->helperText('Use to search products')->placeholder('mashle'),
                        TextInput::make('compare_keywords')->helperText('Separated by comma, use to match the product title')->placeholder('mashle, one punch man'),
                        TextInput::make('suffix')->placeholder('THAO123'),
                        Repeater::make('collections_config')
                            ->itemLabel(fn ($state) => $state['collection_name'] ?? 'New Collection')
                            ->schema([
                                TextInput::make('collection_id')
                                    ->label('Id')
                                    ->grow(false)
                                    ->readOnly(),
                                TextInput::make('ia_code')->readOnly()->label('RB iaCode'),
                                TextInput::make('style')->label('RB Style'),
                                TextInput::make('price'),
                                TextInput::make('limit_products'),
                                Select::make('combination_id')
                                    ->label('Combination')
                                    ->options($this->getCombinationOptions())
                                    ->searchable(),
                            ])
                            ->columnSpanFull()
                            ->columns(6)
                            ->addable(false)
                            ->reorderable(false)
                    ]),

                Actions::make([
                    Actions\Action::make('submit')
                        ->label('Roll Out')
                        ->action($this->submit(...))
                ])
            ]);
    }

    protected function getCombinationOptions(): array
    {
        return Cache::remember('ce_combinations', 3 * 60, function () {
            return collect(app(CustomEdge::class)->combinations())->pluck('name', 'id')->toArray();
        });
    }

    protected function submit(): void
    {
        $data = $this->form->getState();

        foreach ($data['collections_config'] as $config) {
            CrawlForCollection::dispatch(
                new CrawlCollectionConfig(
                    keyword: $data['search_keyword'],
                    compareKeywords: array_map('trim', explode(',', $data['compare_keywords'])),
                    suffix: $data['suffix'],
                    collectionId: $config['collection_id'],
                    iaCode: $config['ia_code'],
                    style: $config['style'],
                    price: $config['price'],
                    limitProducts: $config['limit_products'],
                    combinationId: $config['combination_id'],
                )
            );
        }

        Notification::make()
            ->title('Crawling dispatched!')
            ->success()
            ->send();

        activity('redbubble-importer')
            ->enableLogging()
            ->withProperties($data)
            ->log('Auto import redbubble');
    }
}
