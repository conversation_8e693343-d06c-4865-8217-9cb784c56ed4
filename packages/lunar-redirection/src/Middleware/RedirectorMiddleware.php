<?php

namespace Swebvn\LunarRedirection\Middleware;

use Illuminate\Http\Request;
use Closure;
use Swebvn\LunarRedirection\RedirectSettings;

class RedirectorMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $settings = app(RedirectSettings::class);

        if (!$settings->enabled) {
            return $next($request);
        }

        $path = $request->path();

        // Exclude specific paths from regex matching
        $excludedPaths = ['lunar', 'horizon'];
        if (in_array($path, $excludedPaths)) {
            return $next($request);
        }

        $rules = $settings->rules;

        foreach ($rules as $rule) {
            $matched = $this->matchesRule($rule['from'], $path);

            if ($matched) {
                return redirect($rule['to'], $rule['status_code'] ?? 301);
            }
        }

        return $next($request);
    }

    /**
     * Check if a path matches a rule using regex comparison.
     *
     * @param string $pattern The rule pattern (regex)
     * @param string $path The request path
     * @return bool
     */
    private function matchesRule(string $pattern, string $path): bool
    {
        dump("  >> matchesRule called");
        dump("  >> Pattern:", $pattern);
        dump("  >> Path:", $path);
        dump("  >> Path with slash:", '/' . $path);

        // Test if pattern is a valid regex by checking if preg_match returns false (error)
        set_error_handler(function() {});
        $testResult = preg_match($pattern, 'test');
        restore_error_handler();

        $isValidRegex = $testResult !== false;
        dump("  >> Is valid regex:", $isValidRegex);

        if (!$isValidRegex) {
            // If not a valid regex, fall back to strict comparison
            $exactMatch = $pattern === $path;
            $slashMatch = $pattern === '/' . $path;
            dump("  >> Exact match ($pattern === $path):", $exactMatch);
            dump("  >> Slash match ($pattern === /" . $path . "):", $slashMatch);

            $result = $exactMatch || $slashMatch;
            dump("  >> Final exact result:", $result);
            return $result;
        }

        // Use regex matching
        $regexMatch = preg_match($pattern, $path);
        $regexSlashMatch = preg_match($pattern, '/' . $path);
        dump("  >> Regex match against path:", (bool) $regexMatch);
        dump("  >> Regex match against /path:", (bool) $regexSlashMatch);

        $result = (bool) ($regexMatch || $regexSlashMatch);
        dump("  >> Final regex result:", $result);
        return $result;
    }
}
