<?php

namespace Swebvn\LunarRedirection\Middleware;

use Illuminate\Http\Request;
use Closure;
use Swebvn\LunarRedirection\RedirectSettings;

class RedirectorMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $settings = app(RedirectSettings::class);

        if (!$settings->enabled) {
            return $next($request);
        }

        $path = $request->path();

        // Exclude specific paths from regex matching
        $excludedPaths = ['lunar', 'horizon'];
        if (in_array($path, $excludedPaths)) {
            return $next($request);
        }

        $rules = $settings->rules;

        foreach ($rules as $rule) {
            $matched = $this->matchesRule($rule['from'], $path);

            if ($matched) {
                return redirect($rule['to'], $rule['status_code'] ?? 301);
            }
        }

        return $next($request);
    }

    /**
     * Check if a path matches a rule using regex comparison.
     *
     * @param string $pattern The rule pattern (regex)
     * @param string $path The request path
     * @return bool
     */
    private function matchesRule(string $pattern, string $path): bool
    {
        // Normalize paths - ensure both start with /
        $normalizedPattern = '/' . ltrim($pattern, '/');
        $normalizedPath = '/' . ltrim($path, '/');

        dump("Pattern:", $pattern, "-> Normalized:", $normalizedPattern);
        dump("Path:", $path, "-> Normalized:", $normalizedPath);

        // Handle wildcard matching
        if (str_ends_with($normalizedPattern, '/*')) {
            // Remove /* from pattern
            $patternPrefix = rtrim($normalizedPattern, '/*');
            $matches = str_starts_with($normalizedPath, $patternPrefix);
            dump("Wildcard match: '$normalizedPath' starts with '$patternPrefix'?", $matches);
            return $matches;
        }

        // Exact match
        $matches = $normalizedPattern === $normalizedPath;
        dump("Exact match: '$normalizedPattern' === '$normalizedPath'?", $matches);
        return $matches;
    }
}
