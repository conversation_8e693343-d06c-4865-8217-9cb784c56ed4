<?php

namespace Swebvn\LunarRedirection\Middleware;

use Illuminate\Http\Request;
use Closure;
use Swebvn\LunarRedirection\RedirectSettings;

class RedirectorMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $settings = app(RedirectSettings::class);

        dump('=== REDIRECTION MIDDLEWARE DEBUG ===');
        dump('Settings enabled:', $settings->enabled);
        dump('Request URL:', $request->url());
        dump('Request path:', $request->path());
        dump('Request full URL:', $request->fullUrl());

        if (!$settings->enabled) {
            dump('Middleware disabled, skipping...');
            return $next($request);
        }

        $path = $request->path();
        dump('Processing path:', $path);

        // Exclude specific paths from regex matching
        $excludedPaths = ['lunar', 'horizon'];
        dump('Excluded paths:', $excludedPaths);

        if (in_array($path, $excludedPaths)) {
            dump('Path is excluded, skipping...');
            return $next($request);
        }

        $rules = $settings->rules;
        dump('Total rules:', count($rules));
        dump('Rules:', $rules);

        foreach ($rules as $index => $rule) {
            dump("--- Checking rule #{$index} ---");
            dump('Rule from:', $rule['from'] ?? 'NOT SET');
            dump('Rule to:', $rule['to'] ?? 'NOT SET');
            dump('Rule status:', $rule['status_code'] ?? 301);

            $matched = $this->matchesRule($rule['from'], $path);
            dump('Match result:', $matched);

            if ($matched) {
                dump('MATCH FOUND! Redirecting...');
                return redirect($rule['to'], $rule['status_code'] ?? 301);
            }
        }

        dump('No matches found, continuing...');
        return $next($request);
    }

    /**
     * Check if a path matches a rule using regex comparison.
     *
     * @param string $pattern The rule pattern (regex)
     * @param string $path The request path
     * @return bool
     */
    private function matchesRule(string $pattern, string $path): bool
    {
        dump("  >> matchesRule called");
        dump("  >> Pattern:", $pattern);
        dump("  >> Path:", $path);
        dump("  >> Path with slash:", '/' . $path);

        // Test if pattern is a valid regex
        $isValidRegex = @preg_match($pattern, '') !== false;
        dump("  >> Is valid regex:", $isValidRegex);

        if (!$isValidRegex) {
            // If not a valid regex, fall back to strict comparison
            $exactMatch = $pattern === $path;
            $slashMatch = $pattern === '/' . $path;
            dump("  >> Exact match ($pattern === $path):", $exactMatch);
            dump("  >> Slash match ($pattern === /" . $path . "):", $slashMatch);

            $result = $exactMatch || $slashMatch;
            dump("  >> Final exact result:", $result);
            return $result;
        }

        // Use regex matching
        $regexMatch = preg_match($pattern, $path);
        $regexSlashMatch = preg_match($pattern, '/' . $path);
        dump("  >> Regex match against path:", (bool) $regexMatch);
        dump("  >> Regex match against /path:", (bool) $regexSlashMatch);

        $result = (bool) ($regexMatch || $regexSlashMatch);
        dump("  >> Final regex result:", $result);
        return $result;
    }
}
