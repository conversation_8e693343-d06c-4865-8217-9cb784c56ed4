function ct({state:y,statePath:m,selector:w,plugins:_,external_plugins:v,toolbar:F,text_patterns:A,language:k="en",language_url:C=null,directionality:x="ltr",height:E=null,max_height:O=0,min_height:z=100,width:T=null,max_width:M=0,min_width:P=400,resize:$=!1,skin:B="oxide",content_css:S="default",content_style:D="",toolbar_sticky:W=!0,toolbar_sticky_offset:tt=64,toolbar_mode:et="sliding",toolbar_location:it="auto",inline:nt=!1,toolbar_persist:ot=!1,menubar:G=!1,font_size_formats:K="",fontfamily:V="",relative_urls:H=!0,image_list:J=null,image_advtab:L=!1,image_description:U=!1,image_class_list:j=null,images_upload_url:q=null,images_upload_base_path:p=null,remove_script_host:I=!0,convert_urls:N=!0,custom_configs:Q={},setup:h=null,disabled:st=!1,locale:rt="en",license_key:R="gpl",placeholder:at=null,removeImagesEventCallback:a=null}){let c=window.filamentTinyEditors||{};return{id:null,state:y,statePath:m,selector:w,language:k,language_url:C,directionality:x,height:E,max_height:O,min_height:z,width:T,max_width:M,min_width:P,resize:$,skin:B,content_css:S,content_style:D,plugins:_,external_plugins:v,toolbar:F,text_patterns:A,toolbar_sticky:W,menubar:G,relative_urls:H,remove_script_host:I,convert_urls:N,font_size_formats:K,fontfamily:V,setup:h,image_list:J,image_advtab:L,image_description:U,image_class_list:j,images_upload_url:q,images_upload_base_path:p,license_key:R,custom_configs:Q,updatedAt:Date.now(),disabled:st,locale:rt,placeholder:at,removeImagesEventCallback:a,init(){this.delete(),this.initEditor(y.initialValue),window.filamentTinyEditors=c,this.$watch("state",(o,l)=>{o==="<p></p>"&&o!==this.editor()?.getContent()&&(this.editor()&&this.editor().destroy(),this.initEditor(o)),this.editor()?.container&&o!==this.editor()?.getContent()&&(this.updateEditorContent(o||""),this.putCursorToEnd())})},editor(){return tinymce.get(c[this.statePath])},initEditor(o){let l=this,X=this.$wire,Y=V||"Arial=arial,helvetica,sans-serif; Courier New=courier new,courier,monospace; AkrutiKndPadmini=Akpdmi-n",lt={selector:w,language:k,language_url:C,directionality:x,statusbar:!1,promotion:!1,height:E,max_height:O,min_height:z,width:T,max_width:M,min_width:P,resize:$,skin:B,content_css:S,content_style:D,plugins:_,external_plugins:v,toolbar:F,text_patterns:A,toolbar_sticky:W,toolbar_sticky_offset:tt,toolbar_mode:et,toolbar_location:it,inline:nt,toolbar_persist:ot,menubar:G,menu:{file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},font_size_formats:K||"8pt 10pt 12pt 14pt 16pt 18pt 24pt 36pt 48pt",fontfamily:Y,font_family_formats:Y,relative_urls:H,remove_script_host:I,convert_urls:N,image_list:J,image_advtab:L,image_description:U,image_class_list:j,images_upload_url:q,images_upload_base_path:p,license_key:R,...Q,setup:function(t){window.tinySettingsCopy||(window.tinySettingsCopy=[]),t.settings&&!window.tinySettingsCopy.some(n=>n.id===t.settings.id)&&window.tinySettingsCopy.push(t.settings),t.on("blur",function(n){l.updatedAt=Date.now(),l.state=t.getContent()}),t.on("change",function(n){l.updatedAt=Date.now(),l.state=t.getContent()}),t.on("init",function(n){c[l.statePath]=t.id,o!=null&&t.setContent(o)}),t.on("OpenWindow",function(n){let s=n.target.container.closest(".fi-modal");s&&s.setAttribute("x-trap.noscroll","false")}),t.on("CloseWindow",function(n){let s=n.target.container.closest(".fi-modal");s&&s.setAttribute("x-trap.noscroll","isOpen")}),typeof h=="function"&&h(t)},images_upload_handler:(t,n)=>new Promise((s,g)=>{if(!t.blob())return;let d=(i,e)=>i?i.replace(/\/$/,"")+"/"+e.replace(/^\//,""):e,Z=()=>{X.getFormComponentFileAttachmentUrl(m).then(i=>{if(!i){g("Image upload failed");return}s(d(p,i))})},r=()=>{},u=i=>{n(i.detail.progress)};X.upload(`componentFileAttachments.${m}`,t.blob(),Z,r,u)}),init_instance_callback:function(t){var n=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver,s=a&&typeof a=="function";if(s){var g=new n(function(d,Z){var r=[];d.forEach(function(i){Array.from(i.addedNodes).forEach(function(e){if(e.nodeName==="IMG"&&e.className!=="mce-clonedresizable"){if(r.indexOf(e.src)>=0)return;r.push(e.getAttribute("src"));return}var b=e.getElementsByTagName("img");Array.from(b).forEach(function(f){r.indexOf(f.src)>=0||r.push(f.getAttribute("src"))})})});var u=[];d.forEach(function(i){Array.from(i.removedNodes).forEach(function(e){if(e.nodeName==="IMG"&&e.className!=="mce-clonedresizable"){if(u.indexOf(e.src)>=0)return;u.push(e.getAttribute("src"));return}if(e.nodeType===1){var b=e.getElementsByTagName("img");Array.from(b).forEach(function(f){r.indexOf(f.src)>=0||r.push(f.getAttribute("src"))})}})}),u.forEach(function(i){r.indexOf(i)>=0||a&&typeof a=="function"&&a(i)})});g.observe(t.getBody(),{childList:!0,subtree:!0})}},automatic_uploads:!0};tinymce.init(lt)},updateEditorContent(o){this.editor().setContent(o)},putCursorToEnd(){this.editor().selection.select(this.editor().getBody(),!0),this.editor().selection.collapse(!1)},delete(){c[this.statePath]&&(this.editor().destroy(),delete c[this.statePath])}}}export{ct as default};
