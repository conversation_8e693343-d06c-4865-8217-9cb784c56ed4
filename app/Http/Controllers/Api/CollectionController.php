<?php 

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Collection;
use App\Http\Resources\Collection as CollectionResource;

class CollectionController extends Controller
{
    public function index()
    {
        $collections = Collection::query()
            ->whereHas('products')
            ->latest('id')
            ->paginate(100);
        return CollectionResource::collection($collections);
    }
}