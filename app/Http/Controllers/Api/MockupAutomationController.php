<?php 

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Swebvn\LunarMockupGenerator\Actions\GenerateMockupForCollectionV2;
use Swebvn\LunarMockupGenerator\Mockup;

class MockupAutomationController extends Controller
{
    public function generateMockupProductsV2(Request $request)
    {
        $validated = $request->validate([
            'payload.type_code'   => 'required|string',
            'payload.mockup_name' => 'required|string',
            'payload.design_code' => 'nullable|string',
            'result'              => 'required|string', // URL of the product image
            'design_image'        => 'required|string', // URL of the design image
            'status'              => 'required|string|in:done,processing,failed',
        ]);

        $data = $validated['payload'];
        $typeCode   = $data['type_code'];
        $mockupName = $data['mockup_name'];
        $designCode = $data['design_code'] ?? null;

        $productImage = $validated['result'];
        $designImage  = $validated['design_image'];
        $status       = $validated['status'];

        if ($status === 'done') {
            GenerateMockupForCollectionV2::dispatch($typeCode, $mockupName, $productImage, $designImage, $designCode);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Mockup generation initiated',
            'data' => [
                'mockup_name' => $mockupName,
                'type_code' => $typeCode,
                'product_image' => $productImage,
            ],
        ]);
    }
}