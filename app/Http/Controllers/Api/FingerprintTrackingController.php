<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Settings\Site;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Actions\System\ClearCaches;

class FingerprintTrackingController extends Controller
{
    public function show(Site $settings): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'fingerprint_tracking_enabled' => $settings->fingerprintTrackingEnabled(),
            ],
        ]);
    }

    public function update(Request $request, Site $settings): JsonResponse
    {
        $validated = $request->validate([
            'enabled' => 'required|boolean',
        ]);

        if ($validated['enabled']) {
            $settings->enableFingerprintTracking();
        } else {
            $settings->disableFingerprintTracking();
        }

        ClearCaches::make()->clearPageCache();

        return response()->json([
            'success' => true,
            'message' => 'Fingerprint tracking setting updated successfully.',
            'data' => [
                'fingerprint_tracking_enabled' => $settings->fingerprintTrackingEnabled(),
            ],
        ]);
    }
}