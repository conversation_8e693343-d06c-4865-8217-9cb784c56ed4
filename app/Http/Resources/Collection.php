<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\Collection
 */
class Collection extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'            => $this->id,
            'name'          => $this->attr('name'),
            'description'   => $this->attr('description'),
            'thumbnail_url' => $this->media->first() ? $this->media->first()->getUrl('small') : null,
            'url'           => route('collections.show', $this->resource),
        ];
    }
}
