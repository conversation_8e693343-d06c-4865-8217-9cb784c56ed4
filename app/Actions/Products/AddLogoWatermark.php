<?php

namespace App\Actions\Products;

use App\Actions\CheckImageUrl;
use Lorisleiva\Actions\Concerns\AsAction;
use App\Models\Product;

class AddLogoWatermark
{
    use AsAction;

    public function handle(Product $product, bool $deleteOld = false)
    {
        $thumbnail = $product->thumbnail;
        $logo = site()->logoSrc();

        $newImage = 'https://og.customedge.co/image.png?' . http_build_query([
            'template' => 'product-logo',
            'image'    => $thumbnail->getFullUrl(),
            'logo'     => $logo,
        ]);

        $thumbnail->update([
            'custom_properties->primary' => false,
        ]);

        $newThumbnail = $product->addMediaFromUrl($newImage)
            ->withCustomProperties(['primary' => true])
            ->toMediaCollection('images');

        // ensure the new thumbnail image valid.
        if (CheckImageUrl::make()->handle($newThumbnail->getUrl()) !== false) {
            if ($deleteOld) {
                $thumbnail->delete();
            } else {
                $thumbnail->update([
                    'custom_properties->primary' => false,
                    'collection_name' => 'old_thumbnails',
                ]);
            }
        }

        $product->update(['meta->watermark_added' => true]);
    }
}
